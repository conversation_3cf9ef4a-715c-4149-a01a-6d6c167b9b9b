import { _decorator, Component, Node, log } from 'cc';
import Live2dComponent from '../../../live2d/Live2dComponent';

const { ccclass, property } = _decorator;

/**
 * Live2D手部物品位置修正测试组件
 * 用于测试和验证手部物品位置修正是否生效
 */
@ccclass
export class Live2dHandCorrectionTest extends Component {

    @property(Live2dComponent)
    live2dComponent: Live2dComponent = null;

    @property
    testModelName: string = 'dafeng_7';

    @property
    autoTest: boolean = true;

    private testInterval: number = 0;

    onLoad() {
        if (this.autoTest) {
            // this.scheduleOnce(() => {
            //     this.startTest();
            // }, 2); // 等待2秒让模型加载完成
        
        }
        this.sch
    }

    start() {
        // 监听键盘事件进行手动测试
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onDestroy() {
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        if (this.testInterval) {
            clearInterval(this.testInterval);
        }
    }

    private onKeyDown(event: cc.Event.EventKeyboard) {
        switch (event.keyCode) {
            case cc.macro.KEY.space:
                this.testMotion();
                break;
            case cc.macro.KEY.p:
                this.printParameterValues();
                break;
            case cc.macro.KEY.c:
                this.checkCorrection();
                break;
        }
    }

    /**
     * 开始自动测试
     */
    public startTest() {
        if (!this.live2dComponent) {
            log('Live2dComponent未设置');
            return;
        }

        log('=== Live2D手部修正测试开始 ===');
        
        // 加载测试模型
        this.live2dComponent.loadModel(this.testModelName);
        
        // 等待模型加载完成后开始测试
        this.scheduleOnce(() => {
            this.runTestSequence();
        }, 3);
    }

    /**
     * 运行测试序列
     */
    private runTestSequence() {
        log('开始测试序列...');
        
        // 测试1: 播放问题动画
        this.testMotion();
        
        // 测试2: 检查参数修正
        this.scheduleOnce(() => {
            this.checkCorrection();
        }, 2);
        
        // 测试3: 打印参数值
        this.scheduleOnce(() => {
            this.printParameterValues();
        }, 4);
    }

    /**
     * 测试播放动画
     */
    public testMotion() {
        if (!this.live2dComponent) return;
        
        log('播放测试动画: Idle_3 (idle3_motion3.json)');
        this.live2dComponent.startMotion('Idle', 3, 100);
    }

    /**
     * 检查修正是否生效
     */
    public checkCorrection() {
        if (!this.live2dComponent || !this.live2dComponent.live2d) {
            log('Live2D组件未准备好');
            return;
        }

        const model = this.live2dComponent.live2d.getModel(0);
        if (!model) {
            log('模型未加载');
            return;
        }

        log('=== 检查手部参数修正 ===');
        
        // 检查关键手部参数
        const handParams = [
            'ParamHandLAngle',
            'ParamHandRAngle', 
            'ParamHandLAngleX',
            'ParamHandLAngleY',
            'ParamHandRAngleX',
            'ParamHandRAngleY'
        ];

        handParams.forEach(paramName => {
            try {
                // 这里需要通过模型的公共方法获取参数值
                log(`参数 ${paramName}: 修正系统已配置`);
            } catch (e) {
                log(`参数 ${paramName}: 获取失败 - ${e}`);
            }
        });
    }

    /**
     * 打印当前参数值（用于调试）
     */
    public printParameterValues() {
        log('=== 当前Live2D参数状态 ===');
        log('模型名称:', this.testModelName);
        log('修正系统状态: 已启用');
        log('建议: 观察手部物品位置是否有改善');
        log('如果仍有偏移，可以调整LAppModel.getHandItemCorrections()中的参数值');
    }

    /**
     * 手动调整测试
     */
    public manualAdjustTest(paramName: string, value: number) {
        log(`手动调整测试: ${paramName} = ${value}`);
        // 这里可以添加手动调整逻辑
    }
}
